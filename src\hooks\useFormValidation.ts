import { useState, useCallback } from 'react';

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: unknown) => string | null;
}

export interface ValidationRules {
  [key: string]: ValidationRule;
}

export interface ValidationErrors {
  [key: string]: string;
}

export interface FormValidationResult {
  errors: ValidationErrors;
  isValid: boolean;
  validateField: (name: string, value: unknown) => string | null;
  validateForm: (data: Record<string, unknown>) => boolean;
  clearError: (name: string) => void;
  clearAllErrors: () => void;
  setError: (name: string, error: string) => void;
}

export function useFormValidation(rules: ValidationRules): FormValidationResult {
  const [errors, setErrors] = useState<ValidationErrors>({});

  const validateField = useCallback((name: string, value: unknown): string | null => {
    const rule = rules[name];
    if (!rule) return null;

    // Required validation
    if (rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      return 'This field is required';
    }

    // Skip other validations if field is empty and not required
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return null;
    }

    // String validations
    if (typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        return `Must be at least ${rule.minLength} characters`;
      }
      
      if (rule.maxLength && value.length > rule.maxLength) {
        return `Must be no more than ${rule.maxLength} characters`;
      }
      
      if (rule.pattern && !rule.pattern.test(value)) {
        return 'Invalid format';
      }
    }

    // Number validations
    if (typeof value === 'number') {
      if (rule.min !== undefined && value < rule.min) {
        return `Must be at least ${rule.min}`;
      }
      
      if (rule.max !== undefined && value > rule.max) {
        return `Must be no more than ${rule.max}`;
      }
    }

    // Custom validation
    if (rule.custom) {
      const customError = rule.custom(value);
      if (customError) return customError;
    }

    return null;
  }, [rules]);

  const validateForm = useCallback((data: Record<string, unknown>): boolean => {
    const newErrors: ValidationErrors = {};
    let isValid = true;

    Object.keys(rules).forEach(fieldName => {
      const error = validateField(fieldName, data[fieldName]);
      if (error) {
        newErrors[fieldName] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  }, [rules, validateField]);

  const clearError = useCallback((name: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[name];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  const setError = useCallback((name: string, error: string) => {
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  }, []);

  const isValid = Object.keys(errors).length === 0;

  return {
    errors,
    isValid,
    validateField,
    validateForm,
    clearError,
    clearAllErrors,
    setError
  };
}

// Common validation rules
export const commonValidationRules = {
  required: { required: true },
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    custom: (value: string) => {
      if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        return 'Please enter a valid email address';
      }
      return null;
    }
  },
  phone: {
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
    custom: (value: string) => {
      if (value && !/^[\+]?[1-9][\d]{0,15}$/.test(value)) {
        return 'Please enter a valid phone number';
      }
      return null;
    }
  },
  price: {
    required: true,
    min: 0,
    custom: (value: number) => {
      if (value !== undefined && (isNaN(value) || value < 0)) {
        return 'Price must be a positive number';
      }
      return null;
    }
  },
  quantity: {
    required: true,
    min: 0,
    custom: (value: number) => {
      if (value !== undefined && (isNaN(value) || value < 0 || !Number.isInteger(value))) {
        return 'Quantity must be a positive integer';
      }
      return null;
    }
  },
  productName: {
    required: true,
    minLength: 2,
    maxLength: 100,
    custom: (value: string) => {
      if (value && value.trim().length < 2) {
        return 'Product name must be at least 2 characters';
      }
      return null;
    }
  },
  description: {
    maxLength: 1000,
    custom: (value: string) => {
      if (value && value.length > 1000) {
        return 'Description must be no more than 1000 characters';
      }
      return null;
    }
  },
  discount: {
    min: 0,
    max: 100,
    custom: (value: string | number) => {
      if (value === '' || value === null || value === undefined) return null;
      
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      if (isNaN(numValue) || numValue < 0 || numValue > 100) {
        return 'Discount must be between 0 and 100';
      }
      return null;
    }
  }
};

// Product form validation rules
export const productValidationRules: ValidationRules = {
  name: commonValidationRules.productName,
  price: commonValidationRules.price,
  quantity: commonValidationRules.quantity,
  description: commonValidationRules.description,
  category: commonValidationRules.required,
  type: commonValidationRules.required,
  discount: commonValidationRules.discount
};
